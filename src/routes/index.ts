import { Router } from 'express';
import userRoutes from './userRoutes';
import sjfRoutes from './sjfRoutes'; // 新增：导入 sjfRoutes
import salesRoutes from './salesRoutes';
// import productRoutes from './productRoutes'; // 将来可以取消注释并添加其他路由

const router: Router = Router();

router.use('/users', userRoutes);
router.use('/sjf', sjfRoutes); // 新增：挂载 sjfRoutes 到 /sjf 路径
router.use('/sales', salesRoutes);
// router.use('/products', productRoutes); // 将来可以取消注释

export default router;