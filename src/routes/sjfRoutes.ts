import express, { Router, Request, Response } from 'express';
import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import  SjfService  from '../services/sjfService'; // 导入服务类
import { PerformanceMonitor } from '../config/performanceConfig';

dayjs.extend(isSameOrAfter);

const router: Router = express.Router();

// 定义处理后的数据类型
interface ProcessedAsinData {
  dates: string;
  buyboxPrice: number | null;
  [key: string]: any; // 用于动态的 subBsr 字段
}

// 定义批量响应的数据类型
interface BatchAsinResult {
  asin: string;
  data: ProcessedAsinData[];
}

// 性能监控辅助函数
function logPerformance(operation: string, startTime: number, itemCount?: number) {
  const duration = Date.now() - startTime;
  const itemInfo = itemCount ? ` (${itemCount} items)` : '';
  console.log(`[Performance] ${operation}${itemInfo}: ${duration}ms`);

  if (duration > 3000) {
    console.warn(`[Performance Warning] ${operation} took ${duration}ms, consider optimization`);
  }
}

/**
 * 处理单个 ASIN 的数据
 * @param apiData API 返回的原始数据
 * @param date 可选的日期过滤参数
 * @returns 处理后的数据数组
 */
function processAsinData(apiData: any, date?: string): ProcessedAsinData[] {
  const { dates, subBsr, buyboxPrice } = apiData;
  const monthlyData = new Map<
    string,
    {
      buyboxPrices: number[];
      subBsr: { [key: string]: (number | null)[] };
    }
  >();

  // 聚合数据
  dates.forEach((dateStr: any, index: number) => {
    const monthKey = dayjs(dateStr).format("YYYY-MM-DD");

    if (!monthlyData.has(monthKey)) {
      monthlyData.set(monthKey, {
        buyboxPrices: [],
        subBsr: Object.keys(subBsr).reduce((acc, key) => {
          acc[key] = [];
          return acc;
        }, {} as { [key: string]: (number | null)[] }),
      });
    }

    const monthData = monthlyData.get(monthKey)!;
    monthData.buyboxPrices.push(buyboxPrice[index]);
    Object.entries(subBsr).forEach(([category, values]) => {
      const val = (values as any[])[index];
      monthData.subBsr[category].push(val !== undefined ? val : null);
    });
  });

  // 计算平均值并格式化结果
  const handleDataResult = Array.from(monthlyData).map(([day, data]) => {
    const validPrices = data.buyboxPrices.filter(
      (p): p is number => typeof p === 'number' && Number.isFinite(p)
    );
    const avgPrice =
      validPrices.length > 0
        ? Number(
            (
              validPrices.reduce((a, b) => a + b, 0) / validPrices.length
            ).toFixed(2)
          )
        : null;

    const avgSubBsr = Object.entries(data.subBsr).reduce(
      (acc, [category, values]) => {
        const validValues = values.filter(
          (v): v is number => typeof v === "number" && Number.isFinite(v)
        );
        acc[category] =
          validValues.length > 0
            ? Math.floor(
                validValues.reduce((a, b) => a + b, 0) / validValues.length
              )
            : null;
        return acc;
      },
      {} as { [key: string]: number | null }
    );

    return {
      dates: day,
      ...avgSubBsr,
      buyboxPrice: avgPrice,
    };
  });

  // 应用日期过滤
  if (date) {
    return handleDataResult.filter((item) => {
      return dayjs(item.dates).isSameOrAfter(dayjs(date), "day");
    });
  }

  return handleDataResult;
}

/**
 * @route POST /list
 * @description 获取并处理 ASIN 列表数据
 * @body {string} country - 国家代码
 * @body {string | string[]} asin - ASIN 或 ASIN 数组
 * @body {string} [date] - 可选日期，格式 YYYY-MM-DD，用于过滤结果
 */
router.post('/list', async (req: Request, res: Response): Promise<void> => {
  const requestStartTime = Date.now();

  try {
    const { country, asin, date } = req.body;

    // 参数校验
    if (!country || !asin) {
      res.status(400).json({ error: "Missing required parameters: country and asin" });
      return;
    }

    // 检查 asin 是否为数组
    const isAsinArray = Array.isArray(asin);
    const asinList: string[] = isAsinArray ? asin : [asin];

    // 验证 asin 数组不为空
    if (asinList.length === 0) {
      res.status(400).json({ error: "ASIN array cannot be empty" });
      return;
    }

    // 使用优化的批量请求方法
    const apiStartTime = Date.now();
    const batchResults = await SjfService.getBatchAsinHistory(country, asinList);
    logPerformance('API requests', apiStartTime, asinList.length);

    // 处理批量结果
    const processingStartTime = Date.now();
    const results: BatchAsinResult[] = batchResults.map((result) => {
      if (result.data && result.data.code === 1) {
        const processedData = processAsinData(result.data.data, date);
        return {
          asin: result.asin,
          data: processedData
        };
      } else {
        // 对于失败的请求，返回空数据但保留 asin 信息
        console.warn(`Failed to process ASIN ${result.asin}:`, result.error || 'API returned non-success code');
        return {
          asin: result.asin,
          data: []
        };
      }
    });
    logPerformance('Data processing', processingStartTime, asinList.length);

    // 根据输入类型返回不同格式的数据
    if (isAsinArray) {
      // 如果输入是数组，返回批量格式
      res.json({
        data: results,
        meta: {
          totalRequests: asinList.length,
          successfulRequests: results.filter(r => r.data.length > 0).length,
          processingTime: Date.now() - requestStartTime
        }
      });
    } else {
      // 如果输入是单个字符串，返回原有格式（向后兼容）
      const singleResult = results[0];
      if (singleResult.data.length > 0) {
        res.json({ data: singleResult.data });
      } else {
        res.status(422).json({
          error: "SIF API returned a non-successful business code",
          details: { code: "Failed to process ASIN" }
        });
      }
    }

    // 记录总体性能
    logPerformance('Total request', requestStartTime, asinList.length);


  } catch (error: any) {
    console.error("Error in /sjf/list API:", error);

    // 区分是 SIF API 错误还是其他内部错误
    if (error.message?.includes("SIF API request failed")) {
      res.status(502).json({
        error: "Failed to fetch data from SIF API",
        details: error.message
      });
      return;
    }

    // 处理 Promise.all 中的部分失败情况
    if (error.name === 'AggregateError' || error.errors) {
      res.status(207).json({
        error: "Some ASIN requests failed",
        details: "Partial success - some ASINs could not be processed",
        partialResults: true
      });
      return;
    }

    res.status(500).json({
      error: "Failed to process request",
      details: error.message
    });
    return;
  }
});

/**
 * @route GET /stats
 * @description 获取性能统计信息
 */
router.get('/stats', (req: Request, res: Response): void => {
  try {
    const stats = PerformanceMonitor.getAllStats();
    res.json({
      performanceStats: stats,
      timestamp: new Date().toISOString(),
      message: "Performance statistics for SJF API requests"
    });
  } catch (error: any) {
    console.error("Error getting performance stats:", error);
    res.status(500).json({
      error: "Failed to retrieve performance statistics",
      details: error.message
    });
  }
});

/**
 * @route DELETE /stats
 * @description 清除性能统计信息
 */
router.delete('/stats', (req: Request, res: Response): void => {
  try {
    PerformanceMonitor.clearStats();
    res.json({
      message: "Performance statistics cleared successfully",
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    console.error("Error clearing performance stats:", error);
    res.status(500).json({
      error: "Failed to clear performance statistics",
      details: error.message
    });
  }
});

export default router;