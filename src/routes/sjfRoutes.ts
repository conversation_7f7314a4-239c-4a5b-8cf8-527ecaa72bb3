import express, { Router, Request, Response } from 'express';
import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import  SjfService  from '../services/sjfService'; // 导入服务类

dayjs.extend(isSameOrAfter);

const router: Router = express.Router();

/**
 * @route POST /list
 * @description 获取并处理 ASIN 列表数据
 * @body {string} country - 国家代码
 * @body {string} asin - ASIN
 * @body {string} [date] - 可选日期，格式 YYYY-MM-DD，用于过滤结果
 */
router.post('/list', async (req: Request, res: Response): Promise<void> => {
  try {
    const { country, asin, date } = req.body;

    // 参数校验 (基础示例，可以根据需要增强)
    if (!country || !asin) {
      res.status(400).json({ error: "Missing required parameters: country and asin" });
      return;
    }

    const response = await SjfService.getAsinHistory(country, asin);
    const { code, data: apiData } = response; // 重命名 data 为 apiData 以避免与后续变量冲突

    if (code === 1) {
      const { dates, subBsr, buyboxPrice } = apiData;
      const monthlyData = new Map<
        string,
        {
          buyboxPrices: number[];
          subBsr: { [key: string]: (number | null)[] };
        }
      >();

      dates.forEach((dateStr: any, index: number) => {
        // 源数据中的 dates 是 YYYY-MM-DD HH:mm:ss 格式，这里直接使用它作为 key
        // 如果需要严格按月，则 const monthKey = dayjs(dateStr).format("YYYY-MM");
        const monthKey = dayjs(dateStr).format("YYYY-MM-DD"); // 保持与原逻辑一致，按天聚合

        if (!monthlyData.has(monthKey)) {
          monthlyData.set(monthKey, {
            buyboxPrices: [],
            subBsr: Object.keys(subBsr).reduce((acc, key) => {
              acc[key] = [];
              return acc;
            }, {} as { [key: string]: (number | null)[] }),
          });
        }

        const monthData = monthlyData.get(monthKey)!;
        monthData.buyboxPrices.push(buyboxPrice[index]);
        Object.entries(subBsr).forEach(([category, values]) => {
          const val = (values as any[])[index];
          monthData.subBsr[category].push(val !== undefined ? val : null);
        });
      });

      const handleDataResult = Array.from(monthlyData).map(([day, data]) => { // 重命名 month 为 day
        const validPrices = data.buyboxPrices.filter(
          (p): p is number => typeof p === 'number' && Number.isFinite(p)
        );
        const avgPrice =
          validPrices.length > 0
            ? Number(
                (
                  validPrices.reduce((a, b) => a + b, 0) / validPrices.length
                ).toFixed(2)
              )
            : null;

        const avgSubBsr = Object.entries(data.subBsr).reduce(
          (acc, [category, values]) => {
            const validValues = values.filter(
              (v): v is number => typeof v === "number" && Number.isFinite(v)
            );
            acc[category] =
              validValues.length > 0
                ? Math.floor(
                    validValues.reduce((a, b) => a + b, 0) / validValues.length
                  )
                : null;
            return acc;
          },
          {} as { [key: string]: number | null }
        );

        return {
          dates: day, // 使用聚合的日期键
          ...avgSubBsr, // 扁平化 subBsr
          buyboxPrice: avgPrice,
        };
      });

      if (date) {
        const filterData = handleDataResult.filter((item) => {
          return dayjs(item.dates).isSameOrAfter(dayjs(date), "day");
        });
        res.json({ data: filterData });
        return;
      } else {
        res.json({ data: handleDataResult });
        return;
      }
    } else {
      // API 返回非成功状态码 (业务逻辑上的失败，但请求本身可能成功)
      // 使用 422 Unprocessable Entity 或者 400 Bad Request 更合适
      // details: response 包含了整个 sif api 的响应体，可能包含敏感信息，这里只返回 code
      res.status(422).json({ error: "SIF API returned a non-successful business code", details: { code } });
      return;
    }
  } catch (error: any) {
    console.error("Error in /sjf/list API:", error);
    // 区分是 SIF API 错误还是其他内部错误
    if (error.message?.includes("SIF API request failed")) {
        res.status(502).json({ error: "Failed to fetch data from SIF API", details: error.message });
        return;
    }
    res.status(500).json({ error: "Failed to process request", details: error.message });
    return;
  }
});

export default router;