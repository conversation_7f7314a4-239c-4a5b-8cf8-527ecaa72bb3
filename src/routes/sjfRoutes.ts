import express, { Router, Request, Response } from 'express';
import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import  SjfService  from '../services/sjfService'; // 导入服务类

dayjs.extend(isSameOrAfter);

const router: Router = express.Router();

// 定义处理后的数据类型
interface ProcessedAsinData {
  dates: string;
  buyboxPrice: number | null;
  [key: string]: any; // 用于动态的 subBsr 字段
}

// 定义批量响应的数据类型
interface BatchAsinResult {
  asin: string;
  data: ProcessedAsinData[];
}

/**
 * 处理单个 ASIN 的数据
 * @param apiData API 返回的原始数据
 * @param date 可选的日期过滤参数
 * @returns 处理后的数据数组
 */
function processAsinData(apiData: any, date?: string): ProcessedAsinData[] {
  const { dates, subBsr, buyboxPrice } = apiData;
  const monthlyData = new Map<
    string,
    {
      buyboxPrices: number[];
      subBsr: { [key: string]: (number | null)[] };
    }
  >();

  // 聚合数据
  dates.forEach((dateStr: any, index: number) => {
    const monthKey = dayjs(dateStr).format("YYYY-MM-DD");

    if (!monthlyData.has(monthKey)) {
      monthlyData.set(monthKey, {
        buyboxPrices: [],
        subBsr: Object.keys(subBsr).reduce((acc, key) => {
          acc[key] = [];
          return acc;
        }, {} as { [key: string]: (number | null)[] }),
      });
    }

    const monthData = monthlyData.get(monthKey)!;
    monthData.buyboxPrices.push(buyboxPrice[index]);
    Object.entries(subBsr).forEach(([category, values]) => {
      const val = (values as any[])[index];
      monthData.subBsr[category].push(val !== undefined ? val : null);
    });
  });

  // 计算平均值并格式化结果
  const handleDataResult = Array.from(monthlyData).map(([day, data]) => {
    const validPrices = data.buyboxPrices.filter(
      (p): p is number => typeof p === 'number' && Number.isFinite(p)
    );
    const avgPrice =
      validPrices.length > 0
        ? Number(
            (
              validPrices.reduce((a, b) => a + b, 0) / validPrices.length
            ).toFixed(2)
          )
        : null;

    const avgSubBsr = Object.entries(data.subBsr).reduce(
      (acc, [category, values]) => {
        const validValues = values.filter(
          (v): v is number => typeof v === "number" && Number.isFinite(v)
        );
        acc[category] =
          validValues.length > 0
            ? Math.floor(
                validValues.reduce((a, b) => a + b, 0) / validValues.length
              )
            : null;
        return acc;
      },
      {} as { [key: string]: number | null }
    );

    return {
      dates: day,
      ...avgSubBsr,
      buyboxPrice: avgPrice,
    };
  });

  // 应用日期过滤
  if (date) {
    return handleDataResult.filter((item) => {
      return dayjs(item.dates).isSameOrAfter(dayjs(date), "day");
    });
  }

  return handleDataResult;
}

/**
 * @route POST /list
 * @description 获取并处理 ASIN 列表数据
 * @body {string} country - 国家代码
 * @body {string | string[]} asin - ASIN 或 ASIN 数组
 * @body {string} [date] - 可选日期，格式 YYYY-MM-DD，用于过滤结果
 */
router.post('/list', async (req: Request, res: Response): Promise<void> => {
  try {
    const { country, asin, date } = req.body;

    // 参数校验
    if (!country || !asin) {
      res.status(400).json({ error: "Missing required parameters: country and asin" });
      return;
    }

    // 检查 asin 是否为数组
    const isAsinArray = Array.isArray(asin);
    const asinList: string[] = isAsinArray ? asin : [asin];

    // 验证 asin 数组不为空
    if (asinList.length === 0) {
      res.status(400).json({ error: "ASIN array cannot be empty" });
      return;
    }

    // 批量请求处理
    const promises = asinList.map(async (singleAsin: string): Promise<BatchAsinResult> => {
      const response = await SjfService.getAsinHistory(country, singleAsin);
      const { code, data: apiData } = response;

      if (code === 1) {
        const processedData = processAsinData(apiData, date);
        return {
          asin: singleAsin,
          data: processedData
        };
      } else {
        // 对于失败的请求，返回空数据但保留 asin 信息
        return {
          asin: singleAsin,
          data: []
        };
      }
    });

    // 使用 Promise.all 并行执行所有请求
    const results = await Promise.all(promises);

    // 根据输入类型返回不同格式的数据
    if (isAsinArray) {
      // 如果输入是数组，返回批量格式
      res.json({ data: results });
    } else {
      // 如果输入是单个字符串，返回原有格式（向后兼容）
      const singleResult = results[0];
      if (singleResult.data.length > 0) {
        res.json({ data: singleResult.data });
      } else {
        res.status(422).json({
          error: "SIF API returned a non-successful business code",
          details: { code: "Failed to process ASIN" }
        });
      }
    }

    const response = await SjfService.getAsinHistory(country, asin);
    const { code, data: apiData } = response; // 重命名 data 为 apiData 以避免与后续变量冲突

    if (code === 1) {
      const { dates, subBsr, buyboxPrice } = apiData;
      const monthlyData = new Map<
        string,
        {
          buyboxPrices: number[];
          subBsr: { [key: string]: (number | null)[] };
        }
      >();

      dates.forEach((dateStr: any, index: number) => {
        // 源数据中的 dates 是 YYYY-MM-DD HH:mm:ss 格式，这里直接使用它作为 key
        // 如果需要严格按月，则 const monthKey = dayjs(dateStr).format("YYYY-MM");
        const monthKey = dayjs(dateStr).format("YYYY-MM-DD"); // 保持与原逻辑一致，按天聚合

        if (!monthlyData.has(monthKey)) {
          monthlyData.set(monthKey, {
            buyboxPrices: [],
            subBsr: Object.keys(subBsr).reduce((acc, key) => {
              acc[key] = [];
              return acc;
            }, {} as { [key: string]: (number | null)[] }),
          });
        }

        const monthData = monthlyData.get(monthKey)!;
        monthData.buyboxPrices.push(buyboxPrice[index]);
        Object.entries(subBsr).forEach(([category, values]) => {
          const val = (values as any[])[index];
          monthData.subBsr[category].push(val !== undefined ? val : null);
        });
      });

      const handleDataResult = Array.from(monthlyData).map(([day, data]) => { // 重命名 month 为 day
        const validPrices = data.buyboxPrices.filter(
          (p): p is number => typeof p === 'number' && Number.isFinite(p)
        );
        const avgPrice =
          validPrices.length > 0
            ? Number(
                (
                  validPrices.reduce((a, b) => a + b, 0) / validPrices.length
                ).toFixed(2)
              )
            : null;

        const avgSubBsr = Object.entries(data.subBsr).reduce(
          (acc, [category, values]) => {
            const validValues = values.filter(
              (v): v is number => typeof v === "number" && Number.isFinite(v)
            );
            acc[category] =
              validValues.length > 0
                ? Math.floor(
                    validValues.reduce((a, b) => a + b, 0) / validValues.length
                  )
                : null;
            return acc;
          },
          {} as { [key: string]: number | null }
        );

        return {
          dates: day, // 使用聚合的日期键
          ...avgSubBsr, // 扁平化 subBsr
          buyboxPrice: avgPrice,
        };
      });

      if (date) {
        const filterData = handleDataResult.filter((item) => {
          return dayjs(item.dates).isSameOrAfter(dayjs(date), "day");
        });
        res.json({ data: filterData });
        return;
      } else {
        res.json({ data: handleDataResult });
        return;
      }
    } else {
      // API 返回非成功状态码 (业务逻辑上的失败，但请求本身可能成功)
      // 使用 422 Unprocessable Entity 或者 400 Bad Request 更合适
      // details: response 包含了整个 sif api 的响应体，可能包含敏感信息，这里只返回 code
      res.status(422).json({ error: "SIF API returned a non-successful business code", details: { code } });
      return;
    }
  } catch (error: any) {
    console.error("Error in /sjf/list API:", error);
    // 区分是 SIF API 错误还是其他内部错误
    if (error.message?.includes("SIF API request failed")) {
        res.status(502).json({ error: "Failed to fetch data from SIF API", details: error.message });
        return;
    }
    res.status(500).json({ error: "Failed to process request", details: error.message });
    return;
  }
});

export default router;