import ExcelJS from 'exceljs';

interface IExcelProcessingService {
  processExcelFile(fileBuffer: Buffer): Promise<Buffer>;
  createAsinDataExcel(batchResults: Array<{asin: string, data: any[]}>): Promise<Buffer>;
}

/**
 * Service for processing Excel files.
 */
class ExcelProcessingService implements IExcelProcessingService {
  /**
   * Processes an Excel file buffer to fill empty cells in columns with existing non-null values.
   * @param fileBuffer The buffer of the Excel file.
   * @returns A promise that resolves with the buffer of the processed Excel file.
   */
  public async processExcelFile(fileBuffer: Buffer): Promise<Buffer> {
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(fileBuffer);

    workbook.eachSheet((worksheet: ExcelJS.Worksheet) => {
      if (worksheet.columnCount > 0 && worksheet.rowCount > 0) {
        // 获取实际使用的最大列数，避免处理过多空列
        let maxCol = 0;
        worksheet.eachRow({ includeEmpty: true }, (row: ExcelJS.Row) => { // Ensure all rows are considered for maxCol
            row.eachCell({ includeEmpty: true }, (cell: ExcelJS.Cell, colNumber: number) => { // Ensure all cells are considered
                if (colNumber > maxCol) {
                    maxCol = colNumber;
                }
            });
        });

        for (let colNumber = 1; colNumber <= maxCol; colNumber++) {
          const column = worksheet.getColumn(colNumber);
          const nonNullValues: any[] = [];

          // 收集列中的所有非空值
          // column.values 包含稀疏数组，第一个元素是 undefined (header row?)
          // 因此我们直接遍历单元格
          if (column) { // Check if column exists
            column.eachCell({ includeEmpty: true }, (cell: ExcelJS.Cell) => {
              let cellValue = cell.value;
              let extractedText = '';

              if (cellValue !== null && cellValue !== undefined) {
                if (typeof cellValue === 'object' && (cellValue as ExcelJS.CellRichTextValue).richText) {
                  // 处理富文本
                  (cellValue as ExcelJS.CellRichTextValue).richText.forEach((rt: ExcelJS.RichText) => {
                    extractedText += rt.text;
                  });
                  if (extractedText.trim() !== '') {
                    nonNullValues.push(extractedText.trim());
                  }
                } else if (typeof cellValue === 'string') {
                  // 处理字符串
                  if (cellValue.trim() !== '') {
                    nonNullValues.push(cellValue.trim());
                  }
                } else {
                  // 处理数字、日期等其他类型
                  nonNullValues.push(cellValue);
                }
              }
            });
          }


          if (nonNullValues.length > 0) {
            // 如果列中存在非空值，则用随机选择的非空值填充该列的空单元格
            if (column) { // Check if column exists
                column.eachCell({ includeEmpty: true }, (cell: ExcelJS.Cell) => {
                let isEmpty = false;
                const currentValue = cell.value;

                if (currentValue === null || currentValue === undefined) {
                    isEmpty = true;
                } else if (typeof currentValue === 'string' && currentValue.trim() === '') {
                    isEmpty = true;
                } else if (typeof currentValue === 'object' && (currentValue as ExcelJS.CellRichTextValue).richText) {
                    let textContent = '';
                    (currentValue as ExcelJS.CellRichTextValue).richText.forEach((rt: ExcelJS.RichText) => {
                        textContent += rt.text;
                    });
                    if (textContent.trim() === '') {
                        isEmpty = true;
                    }
                }

                if (isEmpty) {
                    const randomIndex = Math.floor(Math.random() * nonNullValues.length);
                    cell.value = nonNullValues[randomIndex];
                }
                });
            }
          }
        }
      }
    });

    // 将处理后的工作簿写入缓冲区
    const processedBuffer = await workbook.xlsx.writeBuffer();
    return processedBuffer as Buffer; //  writeBuffer() returns a Promise<ArrayBuffer>
  }
/**
   * Processes an Excel file buffer to calculate averages for specific columns in 7-row groups.
   * Skips columns if their header contains specified keywords (e.g., '日期', 'Date').
   * @param fileBuffer The buffer of the Excel file.
   * @param skipColumnKeywords An array of keywords to identify columns to skip. Defaults to ['日期', 'Date'].
   * @returns A promise that resolves with the buffer of the processed Excel file.
   */
  public async processSheetAverages(fileBuffer: Buffer, repetitions: number = 1): Promise<Buffer> {
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(fileBuffer);

    const dateKeywords = ['日期', 'date']; // 不区分大小写的关键词

    workbook.eachSheet((worksheet: ExcelJS.Worksheet) => {
      if (!worksheet) return; // 防御性检查

      const originalRowCount = worksheet.rowCount; // 记录原始数据行数，用于采样
      // 确保即使工作表在逻辑上为空（例如，只有标题行或没有实际数据），maxCol 也有一个合理的值。
      // worksheet.columnCount 可能只计算定义了属性的列。
      // worksheet.actualColumnCount 会考虑有值的单元格延伸到的最远列。
      // 如果两者都不可靠或为0，并且有行，则尝试从第一行获取列数。
      let maxCol = 0;
      if (worksheet.columnCount > 0) {
        maxCol = worksheet.columnCount;
      } else if (worksheet.actualColumnCount > 0) {
        maxCol = worksheet.actualColumnCount;
      } else if (worksheet.rowCount > 0) {
        const firstRow = worksheet.getRow(1);
        maxCol = firstRow.actualCellCount > 0 ? firstRow.actualCellCount : (firstRow.cellCount || 0) ;
      }
      if (maxCol === 0 && worksheet.rowCount > 0) { // 如果还是0，但有行，尝试更健壮的方式
        worksheet.eachRow({ includeEmpty: false }, (row) => {
            if (row.actualCellCount > maxCol) {
                maxCol = row.actualCellCount;
            }
        });
      }
      if (maxCol === 0) { // 如果工作表确实是空的或无法确定列数
        // console.warn(`Sheet "${worksheet.name}" has no columns or is empty, skipping.`);
        return;
      }


      // 收集所有原始数据，按列存储
      const originalDataByColumn: Map<number, any[]> = new Map();
      if (originalRowCount > 0) {
        for (let colIdx = 1; colIdx <= maxCol; colIdx++) {
          const columnValues: any[] = [];
          // 从第二行开始读取原始数据 (索引从1开始，所以是 rowNum = 2)
          for (let rowNum = 2; rowNum <= originalRowCount; rowNum++) {
            const cell = worksheet.getCell(rowNum, colIdx);
            columnValues.push(cell.value); // 存储原始值，包括 null/undefined
          }
          originalDataByColumn.set(colIdx, columnValues);
        }
      }


      // II. “大块平均值追加操作” (针对每个工作表，执行 repetitions 次)
      for (let r = 0; r < repetitions; r++) {
        // 1. 大块操作前置空行
        worksheet.addRow([]); // 在当前所有已存在内容的末尾插入一个空行

        // 2. 生成并追加7条独立的、新计算的“平均值数据行”
        for (let j = 0; j < 7; j++) {
          const newAverageRowData: any[] = new Array(maxCol).fill(null); // 初始化为与列数等长的数组，填充null

          // a. 准备当前这条“平均值数据行”的数据
          for (let colIdx = 1; colIdx <= maxCol; colIdx++) { // colIdx 从1开始
            const headerCell = worksheet.getCell(1, colIdx); // 获取表头单元格
            const headerText = (headerCell.value?.toString() || '').toLowerCase();
            let cellValueForAverageRow: any = null; // 当前平均值行此单元格的值

            // 检查表头是否包含 "日期" 或 "Date"
            if (dateKeywords.some(keyword => headerText.includes(keyword.toLowerCase()))) {
              // 如果当前列是日期列，存入空值 (newAverageRowData 对应位置已是 null)
              // newAverageRowData[colIdx - 1] = null; // 明确设置，或依赖初始化
            } else {
              // 如果当前列是非日期列
              const columnOriginalData = originalDataByColumn.get(colIdx) || [];
              // 从原始数据中提取有效的数字用于抽样
              const validNumericDataForSampling: number[] = [];
              columnOriginalData.forEach(val => {
                if (val !== null && val !== undefined) {
                  let numericVal: number | null = null;
                  if (typeof val === 'number') {
                    numericVal = val;
                  } else if (typeof val === 'string') {
                    const parsed = parseFloat(val.replace(/,/g, '')); // 尝试去除千位分隔符
                    if (!isNaN(parsed)) {
                      numericVal = parsed;
                    }
                  } else if (typeof val === 'object' && val !== null) {
                    if ('result' in val && typeof (val as ExcelJS.CellFormulaValue).result === 'number') {
                       numericVal = (val as ExcelJS.CellFormulaValue).result as number;
                    } else if ('richText' in val) { // 尝试从富文本中提取数字
                        let textContent = '';
                        (val as ExcelJS.CellRichTextValue).richText.forEach(rt => textContent += rt.text);
                        const parsed = parseFloat(textContent.replace(/,/g, ''));
                        if (!isNaN(parsed)) {
                            numericVal = parsed;
                        }
                    }
                  }
                  if (numericVal !== null) {
                    validNumericDataForSampling.push(numericVal);
                  }
                }
              });


              let averageForColumn = 0; // 默认平均值为0
              if (validNumericDataForSampling.length > 0) {
                let sampledValues: number[] = [];
                // 重新进行一次独立的随机不重复抽样，选取7行（或所有行如果少于7行）
                const numToSample = Math.min(7, validNumericDataForSampling.length);
                const tempArray = [...validNumericDataForSampling]; // 创建副本进行抽样

                if (numToSample > 0) { // 只有在有数据可抽样时才进行
                    if (validNumericDataForSampling.length <= 7) {
                      sampledValues = [...tempArray];
                    } else {
                      // Fisher-Yates shuffle variant for sampling k items
                      for (let k = 0; k < numToSample; k++) {
                        const randomIndex = Math.floor(Math.random() * tempArray.length);
                        sampledValues.push(tempArray.splice(randomIndex, 1)[0]);
                      }
                    }
                }


                if (sampledValues.length > 0) {
                  const sum = sampledValues.reduce((acc, val) => acc + val, 0);
                  averageForColumn = sum / sampledValues.length;
                }
              }
              // 将计算得到的平均值存入 newAverageRowData 数组中对应 colIdx 的位置
              newAverageRowData[colIdx - 1] = averageForColumn; // 数组索引是 colIdx - 1
            }
          }
          // b. 追加这条新计算的“平均值数据行”到工作表
          worksheet.addRow(newAverageRowData);
        }
      }
    });

    const processedBuffer = await workbook.xlsx.writeBuffer();
    return processedBuffer as Buffer;
  }

  /**
   * 创建包含ASIN数据的Excel文件
   * @param batchResults 批量ASIN结果数组
   * @returns Promise<Buffer> Excel文件的Buffer
   */
  public async createAsinDataExcel(batchResults: Array<{asin: string, data: any[]}>): Promise<Buffer> {
    const workbook = new ExcelJS.Workbook();

    // 为每个ASIN创建一个工作表
    for (const result of batchResults) {
      const { asin, data } = result;

      // 创建工作表，使用ASIN作为名称
      const worksheet = workbook.addWorksheet(asin);

      if (data && data.length > 0) {
        // 获取第一行数据来确定列名
        const firstRow = data[0];
        const columnNames = Object.keys(firstRow);

        // 设置表头
        worksheet.addRow(columnNames);

        // 设置表头样式
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFE0E0E0' }
        };

        // 添加数据行
        data.forEach(rowData => {
          const values = columnNames.map(col => {
            const value = rowData[col];
            // 处理数字格式
            if (typeof value === 'number') {
              return Number.isInteger(value) ? value : Number(value.toFixed(2));
            }
            return value;
          });
          worksheet.addRow(values);
        });

        // 自动调整列宽
        columnNames.forEach((_, index) => {
          const column = worksheet.getColumn(index + 1);
          let maxLength = columnNames[index].length;

          // 检查数据行的最大长度
          data.forEach(rowData => {
            const cellValue = String(rowData[columnNames[index]] || '');
            if (cellValue.length > maxLength) {
              maxLength = cellValue.length;
            }
          });

          // 设置列宽，最小10，最大50
          column.width = Math.min(Math.max(maxLength + 2, 10), 50);
        });

        // 添加边框
        const range = worksheet.getCell(1, 1).address + ':' +
                     worksheet.getCell(data.length + 1, columnNames.length).address;

        worksheet.getCell(range).border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };

      } else {
        // 如果没有数据，添加一个说明
        worksheet.addRow(['No data available for this ASIN']);
        worksheet.getCell('A1').font = { italic: true, color: { argb: 'FF999999' } };
      }
    }

    // 如果没有任何结果，创建一个默认工作表
    if (batchResults.length === 0) {
      const worksheet = workbook.addWorksheet('No Results');
      worksheet.addRow(['No ASIN data was processed']);
      worksheet.getCell('A1').font = { italic: true, color: { argb: 'FF999999' } };
    }

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer as Buffer;
  }
}

export default new ExcelProcessingService();