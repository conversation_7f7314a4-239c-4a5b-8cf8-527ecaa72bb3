// src/services/sjfService.ts
import {
  API_BASE_URL,
  DEFAULT_HEADERS,
  SUFFIX_PARAMS,
} from '../config/sjfClientConfig'; // 导入配置

/**
 * @description SIF 服务类，封装与 SIF API 交互的逻辑
 */
class SjfService {
  /**
   * @description 从 SIF API 获取 ASIN 历史数据
   * @param country 国家代码，例如 "CA"
   * @param asin ASIN 值，例如 "B0891XFDJ2"
   * @returns Promise<any> API 返回的数据
   */
  public async getAsinHistory(country: string = "CA", asin: string = "B0891XFDJ2"): Promise<any> {
    try {
      const url = `${API_BASE_URL}?country=${country}&asin=${asin}${SUFFIX_PARAMS}`;
      const response = await fetch(url, {
        method: "GET",
        headers: DEFAULT_HEADERS,
      });

      if (!response.ok) {
        throw new Error(`SIF API request failed with status ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error fetching ASIN history from SIF API:", error);
      throw error; // 将错误继续向上抛出，由路由处理函数捕获
    }
  }
}


export default new SjfService();