// src/services/sjfService.ts
import {
  API_BASE_URL,
  DEFAULT_HEADERS,
  SUFFIX_PARAMS,
} from '../config/sjfClientConfig'; // 导入配置
import { PERFORMANCE_CONFIG, PerformanceMonitor } from '../config/performanceConfig';

// 简单的内存缓存
interface CacheEntry {
  data: any;
  timestamp: number;
}

const cache = new Map<string, CacheEntry>();

/**
 * @description SIF 服务类，封装与 SIF API 交互的逻辑
 */
class SjfService {
  private requestQueue: Array<() => Promise<any>> = [];
  private activeRequests = 0;
  private readonly maxConcurrentRequests = PERFORMANCE_CONFIG.MAX_CONCURRENT_REQUESTS;

  /**
   * @description 生成缓存键
   */
  private getCacheKey(country: string, asin: string): string {
    return `${country}-${asin}`;
  }

  /**
   * @description 检查缓存是否有效
   */
  private isCacheValid(entry: CacheEntry): boolean {
    return Date.now() - entry.timestamp < PERFORMANCE_CONFIG.CACHE_TTL;
  }

  /**
   * @description 从缓存获取数据
   */
  private getFromCache(country: string, asin: string): any | null {
    const key = this.getCacheKey(country, asin);
    const entry = cache.get(key);

    if (entry && this.isCacheValid(entry)) {
      return entry.data;
    }

    // 清理过期缓存
    if (entry) {
      cache.delete(key);
    }

    return null;
  }

  /**
   * @description 将数据存入缓存
   */
  private setCache(country: string, asin: string, data: any): void {
    const key = this.getCacheKey(country, asin);
    cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * @description 限流执行请求
   */
  private async executeWithRateLimit<T>(requestFn: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      const execute = async () => {
        if (this.activeRequests >= this.maxConcurrentRequests) {
          // 如果达到并发限制，加入队列
          this.requestQueue.push(execute);
          return;
        }

        this.activeRequests++;
        try {
          const result = await requestFn();
          resolve(result);
        } catch (error) {
          reject(error);
        } finally {
          this.activeRequests--;
          // 处理队列中的下一个请求
          if (this.requestQueue.length > 0) {
            const nextRequest = this.requestQueue.shift();
            if (nextRequest) {
              setTimeout(nextRequest, PERFORMANCE_CONFIG.REQUEST_DELAY);
            }
          }
        }
      };

      execute();
    });
  }

  /**
   * @description 从 SIF API 获取 ASIN 历史数据
   * @param country 国家代码，例如 "CA"
   * @param asin ASIN 值，例如 "B0891XFDJ2"
   * @returns Promise<any> API 返回的数据
   */
  public async getAsinHistory(country: string = "CA", asin: string = "B0891XFDJ2"): Promise<any> {
    const timer = PerformanceMonitor.startTimer(`getAsinHistory-${country}-${asin}`);

    try {
      // 首先检查缓存
      const cachedData = this.getFromCache(country, asin);
      if (cachedData) {
        console.log(`Cache hit for ${country}-${asin}`);
        timer(); // 记录缓存命中时间
        return cachedData;
      }

      // 使用限流执行请求
      return await this.executeWithRateLimit(async () => {
        try {
          const url = `${API_BASE_URL}?country=${country}&asin=${asin}${SUFFIX_PARAMS}`;

          // 添加超时控制
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), PERFORMANCE_CONFIG.REQUEST_TIMEOUT);

          const response = await fetch(url, {
            method: "GET",
            headers: DEFAULT_HEADERS,
            signal: controller.signal,
          });

          clearTimeout(timeoutId);

          if (!response.ok) {
            throw new Error(`SIF API request failed with status ${response.status}`);
          }

          const data = await response.json();

          // 存入缓存
          this.setCache(country, asin, data);

          const duration = timer(); // 记录请求时间
          console.log(`API request for ${country}-${asin} completed in ${duration}ms`);

          return data;
        } catch (error) {
          timer(); // 即使出错也记录时间
          console.error(`Error fetching ASIN history for ${country}-${asin}:`, error);
          throw error;
        }
      });
    } catch (error) {
      timer(); // 确保总是记录时间
      throw error;
    }
  }

  /**
   * @description 批量获取多个 ASIN 的历史数据（优化版本）
   * @param country 国家代码
   * @param asins ASIN 数组
   * @returns Promise<Array<{asin: string, data: any, error?: string}>>
   */
  public async getBatchAsinHistory(country: string, asins: string[]): Promise<Array<{asin: string, data: any, error?: string}>> {
    const results = await Promise.allSettled(
      asins.map(async (asin) => {
        try {
          const data = await this.getAsinHistory(country, asin);
          return { asin, data };
        } catch (error) {
          return {
            asin,
            data: null,
            error: error instanceof Error ? error.message : 'Unknown error'
          };
        }
      })
    );

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          asin: asins[index],
          data: null,
          error: result.reason?.message || 'Request failed'
        };
      }
    });
  }
}

export default new SjfService();