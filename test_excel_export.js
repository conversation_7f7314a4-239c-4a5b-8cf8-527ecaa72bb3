// Excel导出功能测试脚本
const axios = require('axios');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3000/api/sjf';

class ExcelExportTester {
  constructor() {
    this.outputDir = './test_outputs';
    this.ensureOutputDir();
  }

  ensureOutputDir() {
    if (!fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true });
      console.log(`📁 创建输出目录: ${this.outputDir}`);
    }
  }

  async testJsonResponse() {
    console.log('🔍 测试JSON响应格式...');
    try {
      const response = await axios.post(`${BASE_URL}/list`, {
        country: 'CA',
        asin: ['B0891XFDJ2', 'B08EXAMPLE1', 'B08EXAMPLE2'],
        format: 'json'
      });

      console.log('✅ JSON响应成功');
      console.log(`   - 数据条目数: ${response.data.data?.length || 0}`);
      console.log(`   - 响应大小: ${JSON.stringify(response.data).length} 字符`);
      
      return response.data;
    } catch (error) {
      console.error('❌ JSON响应测试失败:', error.response?.data || error.message);
      throw error;
    }
  }

  async testExcelExport() {
    console.log('\n📊 测试Excel导出功能...');
    try {
      const response = await axios.post(`${BASE_URL}/list`, {
        country: 'CA',
        asin: ['B0891XFDJ2', 'B08EXAMPLE1', 'B08EXAMPLE2'],
        format: 'excel'
      }, {
        responseType: 'arraybuffer', // 重要：设置响应类型为arraybuffer
        headers: {
          'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        }
      });

      // 检查响应头
      const contentType = response.headers['content-type'];
      const contentDisposition = response.headers['content-disposition'];
      
      console.log('✅ Excel响应成功');
      console.log(`   - Content-Type: ${contentType}`);
      console.log(`   - Content-Disposition: ${contentDisposition}`);
      console.log(`   - 文件大小: ${response.data.byteLength} 字节`);

      // 保存文件
      const timestamp = new Date().toISOString().replace(/[-:.]/g, '').slice(0, 14);
      const filename = `test_asin_export_${timestamp}.xlsx`;
      const filepath = path.join(this.outputDir, filename);
      
      fs.writeFileSync(filepath, Buffer.from(response.data));
      console.log(`💾 Excel文件已保存: ${filepath}`);

      return { filepath, size: response.data.byteLength };
    } catch (error) {
      console.error('❌ Excel导出测试失败:', error.response?.data || error.message);
      throw error;
    }
  }

  async testSingleAsinExcel() {
    console.log('\n🔍 测试单个ASIN的Excel导出...');
    try {
      const response = await axios.post(`${BASE_URL}/list`, {
        country: 'CA',
        asin: 'B0891XFDJ2',
        format: 'excel'
      }, {
        responseType: 'arraybuffer'
      });

      console.log('✅ 单个ASIN Excel导出成功');
      console.log(`   - 文件大小: ${response.data.byteLength} 字节`);

      // 保存文件
      const timestamp = new Date().toISOString().replace(/[-:.]/g, '').slice(0, 14);
      const filename = `test_single_asin_${timestamp}.xlsx`;
      const filepath = path.join(this.outputDir, filename);
      
      fs.writeFileSync(filepath, Buffer.from(response.data));
      console.log(`💾 单个ASIN Excel文件已保存: ${filepath}`);

      return { filepath, size: response.data.byteLength };
    } catch (error) {
      console.error('❌ 单个ASIN Excel导出测试失败:', error.response?.data || error.message);
      throw error;
    }
  }

  async testInvalidFormat() {
    console.log('\n🚫 测试无效格式参数...');
    try {
      await axios.post(`${BASE_URL}/list`, {
        country: 'CA',
        asin: ['B0891XFDJ2'],
        format: 'invalid'
      });
      
      console.error('❌ 应该返回错误，但请求成功了');
    } catch (error) {
      if (error.response?.status === 400) {
        console.log('✅ 正确拒绝了无效格式参数');
        console.log(`   - 错误信息: ${error.response.data.error}`);
      } else {
        console.error('❌ 意外的错误:', error.response?.data || error.message);
      }
    }
  }

  async testPerformanceComparison() {
    console.log('\n⚡ 性能对比测试...');
    
    const testAsins = ['B0891XFDJ2', 'B08EXAMPLE1', 'B08EXAMPLE2'];
    
    // 测试JSON响应时间
    const jsonStart = Date.now();
    try {
      await axios.post(`${BASE_URL}/list`, {
        country: 'CA',
        asin: testAsins,
        format: 'json'
      });
      const jsonTime = Date.now() - jsonStart;
      console.log(`📊 JSON响应时间: ${jsonTime}ms`);
    } catch (error) {
      console.error('JSON性能测试失败:', error.message);
    }

    // 等待一下
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 测试Excel响应时间
    const excelStart = Date.now();
    try {
      await axios.post(`${BASE_URL}/list`, {
        country: 'CA',
        asin: testAsins,
        format: 'excel'
      }, {
        responseType: 'arraybuffer'
      });
      const excelTime = Date.now() - excelStart;
      console.log(`📊 Excel响应时间: ${excelTime}ms`);
      
      const overhead = excelTime - jsonTime;
      console.log(`📈 Excel格式开销: ${overhead}ms (${((overhead / jsonTime) * 100).toFixed(1)}%)`);
    } catch (error) {
      console.error('Excel性能测试失败:', error.message);
    }
  }

  async runAllTests() {
    console.log('🚀 开始Excel导出功能测试...\n');
    
    try {
      // 基础功能测试
      await this.testJsonResponse();
      await this.testExcelExport();
      await this.testSingleAsinExcel();
      
      // 错误处理测试
      await this.testInvalidFormat();
      
      // 性能对比测试
      await this.testPerformanceComparison();
      
      console.log('\n✅ 所有测试完成！');
      console.log(`📁 测试文件保存在: ${path.resolve(this.outputDir)}`);
      
    } catch (error) {
      console.error('\n❌ 测试过程中出现错误:', error.message);
    }
  }
}

// 运行测试
const tester = new ExcelExportTester();
tester.runAllTests().catch(console.error);
