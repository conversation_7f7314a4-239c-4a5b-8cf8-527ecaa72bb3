// 简单的测试脚本来验证新的 SJF 路由功能
const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api/sjf';

async function testSingleAsin() {
  console.log('测试单个 ASIN...');
  try {
    const response = await axios.post(`${BASE_URL}/list`, {
      country: 'CA',
      asin: 'B0891XFDJ2'
    });
    console.log('单个 ASIN 响应:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error('单个 ASIN 测试失败:', error.response?.data || error.message);
  }
}

async function testMultipleAsins() {
  console.log('\n测试多个 ASIN...');
  try {
    const response = await axios.post(`${BASE_URL}/list`, {
      country: 'CA',
      asin: ['B0891XFDJ2', 'B08EXAMPLE1', 'B08EXAMPLE2']
    });
    console.log('多个 ASIN 响应:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error('多个 ASIN 测试失败:', error.response?.data || error.message);
  }
}

async function testWithDateFilter() {
  console.log('\n测试带日期过滤的单个 ASIN...');
  try {
    const response = await axios.post(`${BASE_URL}/list`, {
      country: 'CA',
      asin: 'B0891XFDJ2',
      date: '2024-01-01'
    });
    console.log('带日期过滤的响应:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error('带日期过滤测试失败:', error.response?.data || error.message);
  }
}

async function runTests() {
  console.log('开始测试 SJF 路由功能...\n');
  
  await testSingleAsin();
  await testMultipleAsins();
  await testWithDateFilter();
  
  console.log('\n测试完成！');
}

// 运行测试
runTests().catch(console.error);
