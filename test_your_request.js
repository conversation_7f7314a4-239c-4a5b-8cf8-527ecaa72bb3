// 测试您的具体请求
const axios = require('axios');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3000/api/sjf';

async function testYourExactRequest() {
  console.log('🧪 测试您的具体请求...');
  console.log('请求参数:');
  console.log(JSON.stringify({
    "country": "CA",
    "asin": ["B0891XFDJ2", "B0891VDLV5", "B08V8T5JWT"],
    "date": "2025-06-01"
  }, null, 2));

  try {
    const response = await axios.post(`${BASE_URL}/list`, {
      "country": "CA",
      "asin": ["B0891XFDJ2", "B0891VDLV5", "B08V8T5JWT"],
      "date": "2025-06-01"
    }, {
      responseType: 'arraybuffer', // 期望Excel文件
      timeout: 30000 // 30秒超时
    });

    // 检查响应
    const contentType = response.headers['content-type'];
    const contentDisposition = response.headers['content-disposition'];
    
    console.log('\n✅ 请求成功！');
    console.log(`📄 Content-Type: ${contentType}`);
    console.log(`📎 Content-Disposition: ${contentDisposition}`);
    console.log(`📊 文件大小: ${response.data.byteLength} 字节`);

    // 验证是否为Excel文件
    if (contentType && contentType.includes('spreadsheetml')) {
      console.log('✅ 确认返回的是Excel文件');
      
      // 保存文件
      const timestamp = new Date().toISOString().replace(/[-:.]/g, '').slice(0, 14);
      const filename = `your_request_result_${timestamp}.xlsx`;
      const outputDir = './test_outputs';
      
      // 确保输出目录存在
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }
      
      const filepath = path.join(outputDir, filename);
      fs.writeFileSync(filepath, Buffer.from(response.data));
      
      console.log(`💾 Excel文件已保存: ${path.resolve(filepath)}`);
      console.log('🎉 测试成功！您的请求现在会返回Excel文件');
      
    } else {
      console.log('❌ 返回的不是Excel文件');
      console.log('响应内容（前100字符）:', 
        Buffer.from(response.data).toString().substring(0, 100));
    }

  } catch (error) {
    console.error('❌ 请求失败:');
    
    if (error.response) {
      console.error(`状态码: ${error.response.status}`);
      console.error(`响应头: ${JSON.stringify(error.response.headers, null, 2)}`);
      
      // 如果是JSON错误响应
      if (error.response.headers['content-type']?.includes('application/json')) {
        try {
          const errorData = JSON.parse(Buffer.from(error.response.data).toString());
          console.error(`错误信息: ${JSON.stringify(errorData, null, 2)}`);
        } catch (parseError) {
          console.error('无法解析错误响应');
        }
      } else {
        console.error('响应数据:', Buffer.from(error.response.data).toString().substring(0, 200));
      }
    } else if (error.request) {
      console.error('网络错误:', error.message);
    } else {
      console.error('其他错误:', error.message);
    }
  }
}

// 同时测试JSON格式（显式指定）
async function testSameRequestWithJson() {
  console.log('\n🔄 测试相同请求但指定JSON格式...');
  
  try {
    const response = await axios.post(`${BASE_URL}/list`, {
      "country": "CA",
      "asin": ["B0891XFDJ2", "B0891VDLV5", "B08V8T5JWT"],
      "date": "2025-06-01",
      "format": "json" // 显式指定JSON
    });

    console.log('✅ JSON格式请求成功');
    console.log(`📊 数据条目数: ${response.data.data?.length || 0}`);
    console.log(`⏱️  处理时间: ${response.data.meta?.processingTime || 'N/A'}ms`);
    console.log(`✅ 成功请求数: ${response.data.meta?.successfulRequests || 'N/A'}`);
    
  } catch (error) {
    console.error('❌ JSON格式请求失败:', error.response?.data || error.message);
  }
}

async function runTest() {
  console.log('🚀 开始测试您的具体请求...\n');
  
  await testYourExactRequest();
  await testSameRequestWithJson();
  
  console.log('\n📋 总结:');
  console.log('- 批量ASIN请求现在默认返回Excel文件');
  console.log('- 如需JSON格式，请添加 "format": "json" 参数');
  console.log('- 单个ASIN请求仍默认返回JSON格式');
}

runTest().catch(console.error);
